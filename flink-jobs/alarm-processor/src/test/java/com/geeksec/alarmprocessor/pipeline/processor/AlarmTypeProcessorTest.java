package com.geeksec.alarmprocessor.pipeline.processor;

import com.geeksec.alarmprocessor.model.Alarm;
import com.geeksec.alarmprocessor.model.AlarmType;
import com.geeksec.alarmprocessor.pipeline.processor.impl.*;
import com.geeksec.common.knowledge.KnowledgeBaseClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 告警类型处理器测试
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
class AlarmTypeProcessorTest {
    
    @Mock
    private KnowledgeBaseClient knowledgeBaseClient;
    
    private MiningVirusProcessor miningVirusProcessor;
    private ScanBehaviorProcessor scanBehaviorProcessor;
    private WebShellProcessor webShellProcessor;
    private PenetrationToolProcessor penetrationToolProcessor;
    private RemoteTrojanProcessor remoteTrojanProcessor;
    private DNSTunnelProcessor dnsTunnelProcessor;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        miningVirusProcessor = new MiningVirusProcessor();
        scanBehaviorProcessor = new ScanBehaviorProcessor();
        webShellProcessor = new WebShellProcessor();
        penetrationToolProcessor = new PenetrationToolProcessor();
        remoteTrojanProcessor = new RemoteTrojanProcessor();
        dnsTunnelProcessor = new DNSTunnelProcessor();
        
        // Mock 知识库客户端
        when(knowledgeBaseClient.getThreatIntelligenceByName(anyString()))
                .thenReturn(createMockThreatIntelligence());
    }
    
    @Test
    void testAlarmTypeProcessorFactory() {
        // 测试工厂方法
        AlarmTypeProcessor processor1 = AlarmTypeProcessorFactory.getProcessor(AlarmType.MINING_VIRUS);
        assertNotNull(processor1);
        assertEquals(AlarmType.MINING_VIRUS, processor1.getSupportedType());
        
        AlarmTypeProcessor processor2 = AlarmTypeProcessorFactory.getProcessor("挖矿病毒");
        assertNotNull(processor2);
        assertEquals(AlarmType.MINING_VIRUS, processor2.getSupportedType());
        
        // 测试未知类型
        AlarmTypeProcessor processor3 = AlarmTypeProcessorFactory.getProcessor(AlarmType.UNKNOWN);
        assertNotNull(processor3);
        assertEquals(AlarmType.UNKNOWN, processor3.getSupportedType());
    }
    
    @Test
    void testMiningVirusProcessor() {
        // 创建挖矿病毒告警
        Alarm alarm = createMiningVirusAlarm();
        
        // 处理告警
        Alarm processedAlarm = miningVirusProcessor.processAlarm(alarm, knowledgeBaseClient);
        
        // 验证处理结果
        assertNotNull(processedAlarm);
        assertNotNull(processedAlarm.getReasonAnalysis());
        assertNotNull(processedAlarm.getHandlingSuggestions());
        assertNotNull(processedAlarm.getExtendedProperties());
        
        // 验证原因分析
        assertFalse(processedAlarm.getReasonAnalysis().getDetailedReasons().isEmpty());
        assertTrue(processedAlarm.getReasonAnalysis().getDetailedReasons().get(0).getDescription()
                .contains("挖矿"));
        
        // 验证处理建议
        assertFalse(processedAlarm.getHandlingSuggestions().getImmediateActions().isEmpty());
        assertTrue(processedAlarm.getHandlingSuggestions().getImmediateActions().get(0)
                .contains("隔离"));
        
        // 验证扩展属性
        assertEquals("99006", processedAlarm.getExtendedProperties().get("model_id"));
        assertEquals("模型", processedAlarm.getExtendedProperties().get("alarm_type"));
    }
    
    @Test
    void testScanBehaviorProcessor() {
        // 创建扫描行为告警
        Alarm alarm = createScanBehaviorAlarm();
        
        // 处理告警
        Alarm processedAlarm = scanBehaviorProcessor.processAlarm(alarm, knowledgeBaseClient);
        
        // 验证处理结果
        assertNotNull(processedAlarm);
        assertNotNull(processedAlarm.getReasonAnalysis());
        assertNotNull(processedAlarm.getHandlingSuggestions());
        
        // 验证原因分析
        assertFalse(processedAlarm.getReasonAnalysis().getDetailedReasons().isEmpty());
        
        // 验证模型ID
        assertEquals("99002", processedAlarm.getExtendedProperties().get("model_id"));
    }
    
    @Test
    void testWebShellProcessor() {
        // 创建WebShell告警
        Alarm alarm = createWebShellAlarm();
        
        // 处理告警
        Alarm processedAlarm = webShellProcessor.processAlarm(alarm, knowledgeBaseClient);
        
        // 验证处理结果
        assertNotNull(processedAlarm);
        assertNotNull(processedAlarm.getReasonAnalysis());
        assertNotNull(processedAlarm.getHandlingSuggestions());
        
        // 验证原因分析包含WebShell特征
        assertTrue(processedAlarm.getReasonAnalysis().getDetailedReasons().get(0).getDescription()
                .contains("中国菜刀"));
        
        // 验证模型ID
        assertEquals("99173", processedAlarm.getExtendedProperties().get("model_id"));
        
        // 验证需要特殊处理
        assertTrue(webShellProcessor.requiresSpecialHandling(alarm));
    }
    
    @Test
    void testPenetrationToolProcessor() {
        // 创建渗透工具指纹告警
        Alarm alarm = createPenetrationToolAlarm();

        // 处理告警
        Alarm processedAlarm = penetrationToolProcessor.processAlarm(alarm, knowledgeBaseClient);

        // 验证处理结果
        assertNotNull(processedAlarm);
        assertNotNull(processedAlarm.getReasonAnalysis());
        assertNotNull(processedAlarm.getHandlingSuggestions());

        // 验证模型ID
        assertEquals("99064", processedAlarm.getExtendedProperties().get("model_id"));
    }

    @Test
    void testRemoteTrojanProcessor() {
        // 创建远控木马告警
        Alarm alarm = createRemoteTrojanAlarm();

        // 处理告警
        Alarm processedAlarm = remoteTrojanProcessor.processAlarm(alarm, knowledgeBaseClient);

        // 验证处理结果
        assertNotNull(processedAlarm);
        assertNotNull(processedAlarm.getReasonAnalysis());
        assertNotNull(processedAlarm.getHandlingSuggestions());

        // 验证需要特殊处理
        assertTrue(remoteTrojanProcessor.requiresSpecialHandling(alarm));

        // 验证模型ID
        assertEquals("99005", processedAlarm.getExtendedProperties().get("model_id"));
    }

    @Test
    void testDNSTunnelProcessor() {
        // 创建DNS隧道告警
        Alarm alarm = createDNSTunnelAlarm();

        // 处理告警
        Alarm processedAlarm = dnsTunnelProcessor.processAlarm(alarm, knowledgeBaseClient);

        // 验证处理结果
        assertNotNull(processedAlarm);
        assertNotNull(processedAlarm.getReasonAnalysis());
        assertNotNull(processedAlarm.getHandlingSuggestions());

        // 验证需要特殊处理
        assertTrue(dnsTunnelProcessor.requiresSpecialHandling(alarm));

        // 验证模型ID
        assertEquals("99091", processedAlarm.getExtendedProperties().get("model_id"));
    }

    @Test
    void testAlarmTypeEnum() {
        // 测试枚举方法
        assertEquals(AlarmType.MINING_VIRUS, AlarmType.fromDisplayName("挖矿病毒"));
        assertEquals(AlarmType.UNKNOWN, AlarmType.fromDisplayName("不存在的类型"));

        // 测试类型检查方法
        assertTrue(AlarmType.MINING_VIRUS.isMiningRelated());
        assertTrue(AlarmType.SCAN_BEHAVIOR.isScanRelated());
        assertTrue(AlarmType.DNS_TUNNEL.isTunnelRelated());
        assertTrue(AlarmType.REMOTE_TROJAN.isC2Related());
        assertTrue(AlarmType.CERTIFICATE_ANOMALY.isCertificateRelated());

        // 测试优先级
        assertEquals(4, AlarmType.MINING_VIRUS.getPriority());
        assertEquals(3, AlarmType.DNS_TUNNEL.getPriority());
        assertEquals(2, AlarmType.SCAN_BEHAVIOR.getPriority());
    }
    
    /**
     * 创建挖矿病毒告警
     */
    private Alarm createMiningVirusAlarm() {
        Alarm alarm = new Alarm();
        alarm.setAlarmId("test-mining-001");
        alarm.setAlarmType("挖矿病毒");
        alarm.setSrcIp("************0");
        alarm.setDstIp("************");
        alarm.setDstPort(8333);
        alarm.setProtocol("TCP");
        alarm.setTimestamp(LocalDateTime.now());
        alarm.setDescription("检测到挖矿病毒活动");
        alarm.setThreatType("挖矿病毒");
        
        // 设置扩展属性
        Map<String, Object> extendedProps = new HashMap<>();
        extendedProps.put("mining_domain", "pool.example.com");
        extendedProps.put("mining_fingerprint", "stratum+tcp");
        alarm.setExtendedProperties(extendedProps);
        
        // 设置处理状态
        alarm.setProcessingStatus(Alarm.ProcessingStatus.builder()
                .isDeduplicationProcessed(false)
                .isFormatted(false)
                .isAttackChainAnalyzed(false)
                .build());
        
        return alarm;
    }
    
    /**
     * 创建扫描行为告警
     */
    private Alarm createScanBehaviorAlarm() {
        Alarm alarm = new Alarm();
        alarm.setAlarmId("test-scan-001");
        alarm.setAlarmType("扫描行为");
        alarm.setSrcIp("**********");
        alarm.setDstIp("********");
        alarm.setTimestamp(LocalDateTime.now());
        alarm.setDescription("检测到端口扫描行为");
        
        // 设置扩展属性
        Map<String, Object> extendedProps = new HashMap<>();
        extendedProps.put("scan_type", "端口扫描sip");
        extendedProps.put("scanned_port_count", "50");
        alarm.setExtendedProperties(extendedProps);
        
        // 设置处理状态
        alarm.setProcessingStatus(Alarm.ProcessingStatus.builder()
                .isDeduplicationProcessed(false)
                .isFormatted(false)
                .isAttackChainAnalyzed(false)
                .build());
        
        return alarm;
    }
    
    /**
     * 创建WebShell告警
     */
    private Alarm createWebShellAlarm() {
        Alarm alarm = new Alarm();
        alarm.setAlarmId("test-webshell-001");
        alarm.setAlarmType("webShell攻击检测");
        alarm.setSrcIp("************");
        alarm.setDstIp("*************");
        alarm.setDstPort(80);
        alarm.setProtocol("HTTP");
        alarm.setTimestamp(LocalDateTime.now());
        alarm.setDescription("检测到WebShell攻击");
        
        // 设置扩展属性
        Map<String, Object> extendedProps = new HashMap<>();
        extendedProps.put("webshell_type", "中国菜刀");
        extendedProps.put("webshell_encrypted", "加密");
        extendedProps.put("webshell_encryption_type", "base64");
        alarm.setExtendedProperties(extendedProps);
        
        // 设置处理状态
        alarm.setProcessingStatus(Alarm.ProcessingStatus.builder()
                .isDeduplicationProcessed(false)
                .isFormatted(false)
                .isAttackChainAnalyzed(false)
                .build());
        
        return alarm;
    }
    
    /**
     * 创建渗透工具指纹告警
     */
    private Alarm createPenetrationToolAlarm() {
        Alarm alarm = new Alarm();
        alarm.setAlarmId("test-penetration-001");
        alarm.setAlarmType("渗透工具指纹");
        alarm.setSrcIp("*************");
        alarm.setDstIp("************");
        alarm.setTimestamp(LocalDateTime.now());
        alarm.setDescription("检测到渗透工具指纹");

        // 设置扩展属性
        Map<String, Object> extendedProps = new HashMap<>();
        extendedProps.put("tool_name", "Nmap");
        extendedProps.put("tool_fingerprint", "Nmap scan");
        alarm.setExtendedProperties(extendedProps);

        // 设置处理状态
        alarm.setProcessingStatus(Alarm.ProcessingStatus.builder()
                .isDeduplicationProcessed(false)
                .isFormatted(false)
                .isAttackChainAnalyzed(false)
                .build());

        return alarm;
    }

    /**
     * 创建远控木马告警
     */
    private Alarm createRemoteTrojanAlarm() {
        Alarm alarm = new Alarm();
        alarm.setAlarmId("test-trojan-001");
        alarm.setAlarmType("远控木马");
        alarm.setSrcIp("************0");
        alarm.setDstIp("************");
        alarm.setDstPort(8080);
        alarm.setProtocol("TCP");
        alarm.setTimestamp(LocalDateTime.now());
        alarm.setDescription("检测到远控木马通信");
        alarm.setThreatType("远控木马");

        // 设置扩展属性
        Map<String, Object> extendedProps = new HashMap<>();
        extendedProps.put("trojan_family", "RAT");
        extendedProps.put("communication_pattern", "定期心跳");
        extendedProps.put("is_encrypted", true);
        alarm.setExtendedProperties(extendedProps);

        // 设置处理状态
        alarm.setProcessingStatus(Alarm.ProcessingStatus.builder()
                .isDeduplicationProcessed(false)
                .isFormatted(false)
                .isAttackChainAnalyzed(false)
                .build());

        return alarm;
    }

    /**
     * 创建DNS隧道告警
     */
    private Alarm createDNSTunnelAlarm() {
        Alarm alarm = new Alarm();
        alarm.setAlarmId("test-dns-tunnel-001");
        alarm.setAlarmType("DNS隐蔽隧道");
        alarm.setSrcIp("*************");
        alarm.setDstIp("*******");
        alarm.setDstPort(53);
        alarm.setProtocol("UDP");
        alarm.setTimestamp(LocalDateTime.now());
        alarm.setDescription("检测到DNS隧道通信");

        // 设置扩展属性
        Map<String, Object> extendedProps = new HashMap<>();
        extendedProps.put("suspicious_domain", "abcdef123456.example.com");
        extendedProps.put("dns_query_type", "TXT");
        extendedProps.put("has_abnormal_pattern", true);
        extendedProps.put("query_frequency", "高频");
        extendedProps.put("has_encoded_data", true);
        extendedProps.put("encoding_type", "Base64");
        alarm.setExtendedProperties(extendedProps);

        // 设置处理状态
        alarm.setProcessingStatus(Alarm.ProcessingStatus.builder()
                .isDeduplicationProcessed(false)
                .isFormatted(false)
                .isAttackChainAnalyzed(false)
                .build());

        return alarm;
    }

    /**
     * 创建模拟威胁情报
     */
    private Map<String, Object> createMockThreatIntelligence() {
        Map<String, Object> threatInfo = new HashMap<>();
        threatInfo.put("name", "挖矿病毒");
        threatInfo.put("category", "恶意软件");
        threatInfo.put("severity", "高");
        threatInfo.put("description", "用于挖掘加密货币的恶意软件");
        return threatInfo;
    }
}
