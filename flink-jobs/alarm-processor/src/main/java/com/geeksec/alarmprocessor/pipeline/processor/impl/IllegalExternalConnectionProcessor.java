package com.geeksec.alarmprocessor.pipeline.processor.impl;

import com.geeksec.alarmprocessor.model.Alarm;
import com.geeksec.alarmprocessor.model.AlarmType;
import com.geeksec.alarmprocessor.pipeline.processor.AbstractAlarmTypeProcessor;
import com.geeksec.common.knowledge.KnowledgeBaseClient;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 违规外联告警处理器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class IllegalExternalConnectionProcessor extends AbstractAlarmTypeProcessor {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    public AlarmType getSupportedType() {
        return AlarmType.ILLEGAL_EXTERNAL_CONNECTION;
    }
    
    @Override
    public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        List<Alarm.DetectionReason> reasons = new ArrayList<>();
        
        String violationType = getViolationType(alarm);
        String targetDomain = getTargetDomain(alarm);
        
        // 违规外联检测
        reasons.add(createDetectionReason(
                "违规外联行为",
                String.format("检测到违反安全策略的外部连接: %s", violationType),
                "合规的网络连接",
                String.format("目标: %s", targetDomain != null ? targetDomain : alarm.getDstIp()),
                "授权的外部服务",
                7
        ));
        
        // 策略违反分析
        String policyViolation = getPolicyViolation(alarm);
        if (policyViolation != null) {
            reasons.add(createDetectionReason(
                    "安全策略违反",
                    String.format("违反了以下安全策略: %s", policyViolation),
                    "符合安全策略",
                    "策略违反",
                    "策略合规",
                    6
            ));
        }
        
        // 数据传输分析
        if (hasDataTransfer(alarm)) {
            reasons.add(createDetectionReason(
                    "可疑数据传输",
                    "检测到向外部服务器传输大量数据",
                    "正常数据传输",
                    String.format("传输量: %s", getDataTransferSize(alarm)),
                    "少量业务数据",
                    8
            ));
        }
        
        return reasons;
    }
    
    @Override
    public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        String violationType = getViolationType(alarm);
        
        return Alarm.HandlingSuggestions.builder()
                .immediateActions(Arrays.asList(
                        "立即阻断违规的外部连接",
                        String.format("检查%s主机的连接原因和用途", alarm.getSrcIp()),
                        "审查相关用户的操作记录",
                        "评估数据泄露风险"
                ))
                .investigationSteps(Arrays.asList(
                        String.format("分析%s违规连接的具体目的", violationType),
                        "确认是否为恶意行为或误操作",
                        "检查是否有敏感数据传输",
                        "分析连接的时间模式和频率",
                        "确认相关用户的身份和权限"
                ))
                .preventiveMeasures(Arrays.asList(
                        "更新网络安全策略和访问控制规则",
                        "加强出口网络流量监控",
                        "实施数据防泄露(DLP)解决方案",
                        "加强员工安全意识培训",
                        "定期审查和更新外联白名单"
                ))
                .recoverySteps(Arrays.asList(
                        "修复导致违规外联的安全问题",
                        "更新防火墙和代理服务器配置",
                        "加强对类似行为的监控",
                        "建立违规外联的告警机制"
                ))
                .build();
    }
    
    @Override
    public String getAlarmPrinciple(Alarm alarm) {
        String violationType = getViolationType(alarm);
        return String.format("内网主机违反安全策略，进行%s等未授权的外部连接，" +
                "可能导致数据泄露、恶意软件下载或建立非法通信通道。", violationType);
    }
    
    @Override
    public String getDetectionPrinciple(Alarm alarm) {
        return "通过监控网络出口流量，对比安全策略和白名单，" +
                "识别违反组织安全策略的外部连接行为。";
    }
    
    @Override
    public List<Map<String, String>> getVictimInfo(Alarm alarm) {
        List<Map<String, String>> victims = new ArrayList<>();
        
        // 发起违规外联的主机可能是受害者（被恶意软件控制）
        if (alarm.getSrcIp() != null) {
            Map<String, String> victim = new HashMap<>();
            victim.put("ip", alarm.getSrcIp());
            victim.put("role", "违规外联主机");
            victim.put("risk_level", "中等");
            victims.add(victim);
        }
        
        return victims;
    }
    
    @Override
    public List<Map<String, String>> getAttackerInfo(Alarm alarm) {
        List<Map<String, String>> attackers = new ArrayList<>();
        
        // 外部目标可能是恶意服务器
        if (alarm.getDstIp() != null) {
            Map<String, String> attacker = new HashMap<>();
            attacker.put("ip", alarm.getDstIp());
            attacker.put("role", "外部目标服务器");
            attacker.put("threat_level", "待确认");
            attackers.add(attacker);
        }
        
        return attackers;
    }
    
    @Override
    public String getModelId(Alarm alarm) {
        return "99009";
    }
    
    /**
     * 获取违规类型
     */
    private String getViolationType(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object violationType = alarm.getExtendedProperties().get("violation_type");
            if (violationType != null) {
                return violationType.toString();
            }
        }
        return "未授权外联";
    }
    
    /**
     * 获取目标域名
     */
    private String getTargetDomain(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object domain = alarm.getExtendedProperties().get("target_domain");
            if (domain != null) {
                return domain.toString();
            }
        }
        return null;
    }
    
    /**
     * 获取策略违反信息
     */
    private String getPolicyViolation(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object policy = alarm.getExtendedProperties().get("policy_violation");
            if (policy != null) {
                return policy.toString();
            }
        }
        return null;
    }
    
    /**
     * 检查是否有数据传输
     */
    private boolean hasDataTransfer(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object hasTransfer = alarm.getExtendedProperties().get("has_data_transfer");
            if (hasTransfer instanceof Boolean) {
                return (Boolean) hasTransfer;
            }
        }
        return false;
    }
    
    /**
     * 获取数据传输大小
     */
    private String getDataTransferSize(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object size = alarm.getExtendedProperties().get("data_transfer_size");
            if (size != null) {
                return size.toString();
            }
        }
        return "未知";
    }
}
