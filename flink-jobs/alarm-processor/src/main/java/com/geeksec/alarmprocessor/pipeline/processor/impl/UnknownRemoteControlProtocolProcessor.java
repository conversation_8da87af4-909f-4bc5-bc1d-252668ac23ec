package com.geeksec.alarmprocessor.pipeline.processor.impl;

import com.geeksec.alarmprocessor.model.Alarm;
import com.geeksec.alarmprocessor.model.AlarmType;
import com.geeksec.alarmprocessor.pipeline.processor.AbstractAlarmTypeProcessor;
import com.geeksec.common.knowledge.KnowledgeBaseClient;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 未知远程控制协议告警处理器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class UnknownRemoteControlProtocolProcessor extends AbstractAlarmTypeProcessor {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    public AlarmType getSupportedType() {
        return AlarmType.UNKNOWN_REMOTE_CONTROL_PROTOCOL;
    }
    
    @Override
    public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        List<Alarm.DetectionReason> reasons = new ArrayList<>();
        
        String unknownProtocol = getUnknownProtocol(alarm);
        String protocolFeatures = getProtocolFeatures(alarm);
        
        // 未知远程控制协议检测
        reasons.add(createDetectionReason(
                "未知远程控制协议",
                String.format("检测到未知类型的远程控制协议: %s", unknownProtocol),
                "已知标准协议",
                protocolFeatures,
                "标准协议特征",
                7
        ));
        
        // 自定义协议特征
        if (hasCustomProtocolFeatures(alarm)) {
            reasons.add(createDetectionReason(
                    "自定义协议特征",
                    "检测到自定义或修改的协议特征",
                    "标准协议实现",
                    getCustomFeatureDescription(alarm),
                    "RFC标准实现",
                    6
            ));
        }
        
        // 加密或混淆检测
        if (hasEncryptionOrObfuscation(alarm)) {
            reasons.add(createDetectionReason(
                    "协议加密混淆",
                    "协议使用了加密或混淆技术",
                    "明文或标准加密",
                    "自定义加密/混淆",
                    "标准加密协议",
                    8
            ));
        }
        
        return reasons;
    }
    
    @Override
    public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        String unknownProtocol = getUnknownProtocol(alarm);
        
        return Alarm.HandlingSuggestions.builder()
                .immediateActions(Arrays.asList(
                        String.format("立即阻断%s未知协议通信", unknownProtocol),
                        "深度分析协议特征和通信内容",
                        "隔离相关主机进行详细检查",
                        "收集协议样本进行逆向分析"
                ))
                .investigationSteps(Arrays.asList(
                        "对未知协议进行逆向工程分析",
                        "确认协议的具体功能和目的",
                        "分析协议的加密和混淆机制",
                        "确认协议开发者和使用者",
                        "评估协议的威胁等级和影响范围"
                ))
                .preventiveMeasures(Arrays.asList(
                        "更新协议识别规则和特征库",
                        "加强对未知协议的检测能力",
                        "实施协议白名单策略",
                        "部署协议分析和沙箱系统",
                        "建立未知协议的威胁情报库"
                ))
                .recoverySteps(Arrays.asList(
                        "阻断所有使用该协议的通信",
                        "清除相关的恶意软件或工具",
                        "更新网络安全设备的检测规则",
                        "加强对类似未知协议的监控"
                ))
                .build();
    }
    
    @Override
    public String getAlarmPrinciple(Alarm alarm) {
        String unknownProtocol = getUnknownProtocol(alarm);
        return String.format("攻击者使用%s等未知或自定义的远程控制协议，" +
                "试图绕过传统的协议检测机制，建立隐蔽的远程控制通道。", unknownProtocol);
    }
    
    @Override
    public String getDetectionPrinciple(Alarm alarm) {
        return "通过机器学习和行为分析技术，识别不符合已知协议标准的网络通信模式，" +
                "发现可能的自定义或未知远程控制协议。";
    }
    
    @Override
    public List<Map<String, Object>> generateAttackRoute(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        List<Map<String, Object>> attackRoute = new ArrayList<>();
        
        // 协议部署
        Map<String, Object> step1 = new HashMap<>();
        step1.put("step", 1);
        step1.put("action", "协议部署");
        step1.put("description", "在目标主机部署自定义远程控制协议");
        attackRoute.add(step1);
        
        // 通信建立
        Map<String, Object> step2 = new HashMap<>();
        step2.put("step", 2);
        step2.put("action", "通信建立");
        step2.put("description", String.format("使用%s协议建立远程控制连接", getUnknownProtocol(alarm)));
        step2.put("protocol", getUnknownProtocol(alarm));
        attackRoute.add(step2);
        
        // 隐蔽控制
        Map<String, Object> step3 = new HashMap<>();
        step3.put("step", 3);
        step3.put("action", "隐蔽控制");
        step3.put("description", "通过自定义协议进行隐蔽的远程控制");
        attackRoute.add(step3);
        
        return attackRoute;
    }
    
    @Override
    public String getModelId(Alarm alarm) {
        return "99169";
    }
    
    @Override
    public boolean requiresSpecialHandling(Alarm alarm) {
        return true; // 未知协议需要特殊处理
    }
    
    /**
     * 获取未知协议名称
     */
    private String getUnknownProtocol(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object protocol = alarm.getExtendedProperties().get("unknown_protocol");
            if (protocol != null) {
                return protocol.toString();
            }
        }
        return "未识别协议";
    }
    
    /**
     * 获取协议特征
     */
    private String getProtocolFeatures(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object features = alarm.getExtendedProperties().get("protocol_features");
            if (features != null) {
                return features.toString();
            }
        }
        return "未知协议特征";
    }
    
    /**
     * 检查是否有自定义协议特征
     */
    private boolean hasCustomProtocolFeatures(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object custom = alarm.getExtendedProperties().get("has_custom_features");
            if (custom instanceof Boolean) {
                return (Boolean) custom;
            }
        }
        return false;
    }
    
    /**
     * 获取自定义特征描述
     */
    private String getCustomFeatureDescription(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object description = alarm.getExtendedProperties().get("custom_feature_description");
            if (description != null) {
                return description.toString();
            }
        }
        return "自定义协议实现";
    }
    
    /**
     * 检查是否有加密或混淆
     */
    private boolean hasEncryptionOrObfuscation(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object encrypted = alarm.getExtendedProperties().get("has_encryption_obfuscation");
            if (encrypted instanceof Boolean) {
                return (Boolean) encrypted;
            }
        }
        return false;
    }
}
